name: Build Stable Image

on:
  push:
    branches:
      - main

jobs:
  docker:
    runs-on: ubuntu-latest
    steps:
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v5
        with:
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/nest-admin-server:stable
          platforms: linux/amd64,linux/arm64
      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}

  # deploy:
  #   runs-on: ubuntu-latest
  #   needs: docker
  #   steps:
  #     - name: Deploy to remote server
  #       uses: appleboy/ssh-action@master
  #       with:
  #         host: ${{ secrets.REMOTE_HOST }}
  #         username: ${{ secrets.REMOTE_USERNAME }}
  #         password: ${{ secrets.REMOTE_PASSWORD }}
  #         port: ${{ secrets.REMOTE_PORT }}
  #         script: |
  #           cd ${{ secrets.REMOTE_WORKDIR }}
  #           pnpm docker:prod:up
  #           docker cp ./public/ nest-admin-server:/nest-admin/public
  #           docker image prune -f
  #           docker container prune -f
