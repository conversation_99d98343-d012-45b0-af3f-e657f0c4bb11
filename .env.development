# logger
LOGGER_LEVEL = debug

# security
JWT_SECRET = admin!@#123
JWT_EXPIRE = 86400 # 单位秒
REFRESH_TOKEN_SECRET = admin!@#123
REFRESH_TOKEN_EXPIRE = 2592000

# swagger
SWAGGER_ENABLE = true
SWAGGER_PATH = api-docs
SWAGGER_VERSION = 1.0

# db
DB_HOST = 127.0.0.1
DB_PORT = 13307
DB_DATABASE = nest_admin
DB_USERNAME = root
DB_PASSWORD = root
DB_SYNCHRONIZE = true
DB_LOGGING = ["error"]

# redis
REDIS_PORT = 6379
REDIS_HOST = 127.0.0.1
REDIS_PASSWORD = 123456
REDIS_DB = 0

# smtp
SMTP_HOST = smtp.163.com
SMTP_PORT = 465
SMTP_USER = <EMAIL>
SMTP_PASS = VIPLLOIPMETTROYU
 